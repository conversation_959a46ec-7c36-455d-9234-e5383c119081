package com.snct.system.service.impl;

import com.snct.common.utils.DateUtils;
import com.snct.system.domain.Device;
import com.snct.system.mapper.DeviceMapper;
import com.snct.system.service.IDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
@Service
public class DeviceServiceImpl implements IDeviceService 
{
    @Autowired
    private DeviceMapper deviceMapper;

    /**
     * 查询设备
     * 
     * @param id 设备主键
     * @return 设备
     */
    @Override
    public Device selectDeviceById(Long id)
    {
        return deviceMapper.selectDeviceById(id);
    }

    /**
     * 查询设备列表
     * 
     * @param device 设备
     * @return 设备
     */
    @Override
    public List<Device> selectDeviceList(Device device)
    {
        return deviceMapper.selectDeviceList(device);
    }

    /**
     * 根据船舶SN和设备类型查询设备列表
     *
     * @param sn 船舶SN
     * @param type 设备类型
     * @return 设备集合
     */
    @Override
    public List<Device> selectSimpleDeviceListBySnAndType(String sn, Long type)
    {
        return deviceMapper.selectSimpleDeviceListBySnAndType(sn, type);
    }

    /**
     * 根据设备编号查询设备
     *
     * @param code 设备编号
     * @return 设备信息
     */
    @Override
    public Device selectDeviceByCode(String code)
    {
        return deviceMapper.selectDeviceByCode(code);
    }

    /**
     * 根据设备编号查询设备
     *
     * @param code 设备编号
     * @return 设备信息
     */
    @Override
    public Device selectDeviceBySnAndCode(String sn, String code)
    {
        return deviceMapper.selectDeviceBySnAndCode(sn,code);
    }

    /**
     * 新增设备
     * 
     * @param device 设备
     * @return 结果
     */
    @Override
    public int insertDevice(Device device)
    {
        device.setCreateTime(DateUtils.getNowDate());
        return deviceMapper.insertDevice(device);
    }

    /**
     * 修改设备
     * 
     * @param device 设备
     * @return 结果
     */
    @Override
    public int updateDevice(Device device)
    {
        device.setUpdateTime(DateUtils.getNowDate());
        return deviceMapper.updateDevice(device);
    }

    /**
     * 批量删除设备
     * 
     * @param ids 需要删除的设备主键
     * @return 结果
     */
    @Override
    public int deleteDeviceByIds(Long[] ids)
    {
        return deviceMapper.deleteDeviceByIds(ids);
    }

    /**
     * 删除设备信息
     * 
     * @param id 设备主键
     * @return 结果
     */
    @Override
    public int deleteDeviceById(Long id)
    {
        return deviceMapper.deleteDeviceById(id);
    }
}
