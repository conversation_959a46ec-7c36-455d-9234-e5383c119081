package com.snct.hbase.domain.hbase;

import com.snct.hbase.annotation.Excel;
import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;

import java.util.HashMap;
import java.util.Map;


/**
 * pdu消息对象 bu_msg_pdu
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@HBaseTable(tableName = "snct:pdu")
public class PduHbaseVo
{
    /** ID */
    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    @HBaseColumn(family = "i", qualifier = "i_b_t")
    private String initialBjTime;

    /** 部门ID */
    @HBaseColumn(family = "i", qualifier = "d_p")
    private Long deptId;

    /** 部门名称 */
    @Excel(name="部门名称")
    @HBaseColumn(family = "i", qualifier = "d_p_n")
    private String deptName;

    /** 船只id */
    @HBaseColumn(family = "i", qualifier = "s_i")
    private Long shipId;

    /** 船只名称 */
    @Excel(name="船只名称")
    @HBaseColumn(family = "i", qualifier = "s_n")
    private String shipName;

    /** 设备id */
    @HBaseColumn(family = "i", qualifier = "d_i")
    private Long deviceId;

    /** 设备名称 */
    @Excel(name="设备名称")
    @HBaseColumn(family = "i", qualifier = "d_n")
    private String deviceName;

    /** 批次编号 */
    @HBaseColumn(family = "i", qualifier = "b_c")
    private String batchCode;

    /** 总电能 */
    @Excel(name = "总电能")
    @HBaseColumn(family = "i", qualifier = "m_g")
    private Double manage;

    /** 电流 */
    @Excel(name = "电流")
    @HBaseColumn(family = "i", qualifier = "e_t")
    private Double electric;

    /** 电压 */
    @Excel(name = "电压")
    @HBaseColumn(family = "i", qualifier = "v_t")
    private Double voltage;

    /** 有功功率 */
    @Excel(name = "有功功率")
    @HBaseColumn(family = "i", qualifier = "y_p")
    private Double yesPwoer;

    /** 无功功率 */
    @Excel(name = "无功功率")
    @HBaseColumn(family = "i", qualifier = "n_p")
    private Double noPwoer;

    /** 视在功率 */
    @Excel(name = "视在功率")
    @HBaseColumn(family = "i", qualifier = "s_p")
    private Double seePwoer;

    /** 功率因数 */
    @Excel(name = "功率因数")
    @HBaseColumn(family = "i", qualifier = "p_p")
    private Long powerParam;

    /** 状态 0默认 1发送云端成功 2发送云端失败 */
    @Excel(name = "状态 0默认 1发送云端成功 2发送云端失败")
    @HBaseColumn(family = "i", qualifier = "s_t")
    private Long status;

    /**
     * 通道数据（动态列簇）
     * 使用Map存储通道数据，键为outIndex，值为通道数据对象
     * 这些数据不会直接存入HBase，而是通过特定方法处理
     */
    private Map<Long, ChannelData> channelDataMap = new HashMap<>();

    /**
     * 获取特定通道的HBase列簇名称
     *
     * @param outIndex 通道索引
     * @return 列簇名称，格式为"o" + outIndex
     */
    public String getOutColumnFamily(Long outIndex) {
        return "o" + outIndex;
    }

    /**
     * 添加通道数据
     *
     * @param outIndex 通道索引
     * @param electric 电流
     * @param power 功率
     * @param outStatus 插座状态
     */
    public void addChannelData(Long outIndex, Double electric, Double power, Long outStatus) {
        ChannelData channelData = new ChannelData();
        channelData.setOutIndex(outIndex);
        channelData.setElectric(electric);
        channelData.setPower(power);
        channelData.setOutStatus(outStatus);

        channelDataMap.put(outIndex, channelData);
    }

    /**
     * 获取所有通道数据
     *
     * @return 通道数据映射
     */
    public Map<Long, ChannelData> getChannelDataMap() {
        return channelDataMap;
    }

    /**
     * 判断是否有通道数据
     *
     * @return 是否有通道数据
     */
    public boolean hasChannelData() {
        return !channelDataMap.isEmpty();
    }

    /**
     * 内部类：通道数据
     */
    public static class ChannelData {
        /** 通道索引 */
        private Long outIndex;

        /** 电流 */
        private Double electric;

        /** 功率 */
        private Double power;

        /** 插座状态 */
        private Long outStatus;

        public Long getOutIndex() {
            return outIndex;
        }

        public void setOutIndex(Long outIndex) {
            this.outIndex = outIndex;
        }

        public Double getElectric() {
            return electric;
        }

        public void setElectric(Double electric) {
            this.electric = electric;
        }

        public Double getPower() {
            return power;
        }

        public void setPower(Double power) {
            this.power = power;
        }

        public Long getOutStatus() {
            return outStatus;
        }

        public void setOutStatus(Long outStatus) {
            this.outStatus = outStatus;
        }
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public Long getShipId() {
        return shipId;
    }

    public void setShipId(Long shipId) {
        this.shipId = shipId;
    }

    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getShipName() {
        return shipName;
    }

    public void setShipName(String shipName) {
        this.shipName = shipName;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getBatchCode() {
        return batchCode;
    }

    public void setBatchCode(String batchCode) {
        this.batchCode = batchCode;
    }

    public Double getManage() {
        return manage;
    }

    public void setManage(Double manage) {
        this.manage = manage;
    }

    public Double getElectric() {
        return electric;
    }

    public void setElectric(Double electric) {
        this.electric = electric;
    }

    public Double getVoltage() {
        return voltage;
    }

    public void setVoltage(Double voltage) {
        this.voltage = voltage;
    }

    public Double getYesPwoer() {
        return yesPwoer;
    }

    public void setYesPwoer(Double yesPwoer) {
        this.yesPwoer = yesPwoer;
    }

    public Double getNoPwoer() {
        return noPwoer;
    }

    public void setNoPwoer(Double noPwoer) {
        this.noPwoer = noPwoer;
    }

    public Double getSeePwoer() {
        return seePwoer;
    }

    public void setSeePwoer(Double seePwoer) {
        this.seePwoer = seePwoer;
    }

    public Long getPowerParam() {
        return powerParam;
    }

    public void setPowerParam(Long powerParam) {
        this.powerParam = powerParam;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }
}