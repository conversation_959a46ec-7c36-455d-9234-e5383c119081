package com.snct.kafka.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 设备处理器工厂类
 * 负责创建不同类型设备的处理器
 *
 * <AUTHOR>
 */
@Component
public class DeviceHandlerFactory {

    /**
     * Kafka消息类型常量
     */
    public static final int TYPE_SHIP_DEVICE = 0;   // 船舶和设备数据
    public static final int TYPE_MODEM = 1;         // 猫数据
    public static final int TYPE_AMPLIFIER = 2;     // 功放数据
    public static final int TYPE_PDU = 3;           // PDU数据
    
    /**
     * 消息代码类型常量
     */
    public static final String CODE_SHIP_DATA = "SHIP_DATA";    // 船舶数据
    public static final String CODE_DEVICE_DATA = "DEVICE_DATA"; // 设备数据
    
    @Autowired
    private ModemDeviceHandler modemHandler;
    
    @Autowired
    private AmplifierDeviceHandler amplifierHandler;
    
    @Autowired
    private PduDeviceHandler pduHandler;
    
    @Autowired
    private ShipDataHandler shipDataHandler;
    
    @Autowired
    private DeviceDataHandler deviceDataHandler;
    
    /**
     * 根据设备类型获取对应的处理器
     *
     * @param deviceType 设备类型
     * @return 设备处理器
     */
    public AbstractDeviceHandler getHandler(int deviceType) {
        switch (deviceType) {
            case TYPE_MODEM:
                return modemHandler;
            case TYPE_AMPLIFIER:
                return amplifierHandler;
            case TYPE_PDU:
                return pduHandler;
            default:
                throw new IllegalArgumentException("不支持的设备类型: " + deviceType);
        }
    }
    
    /**
     * 根据消息类型和代码获取对应的处理器
     *
     * @param messageType 消息类型
     * @param messageCode 消息代码
     * @return 设备处理器
     */
    public AbstractDeviceHandler getHandler(int messageType, String messageCode) {
        if (messageType == TYPE_SHIP_DEVICE) {
            // 处理船舶和设备数据
            if (messageCode.contains(CODE_SHIP_DATA)) {
                return shipDataHandler;
            } else if (messageCode.contains(CODE_DEVICE_DATA)) {
                return deviceDataHandler;
            }
            throw new IllegalArgumentException("不支持的船舶或设备消息代码: " + messageCode);
        } else {
            // 处理设备数据
            return getHandler(messageType);
        }
    }
    
    /**
     * 检查是否支持该设备类型
     *
     * @param deviceType 设备类型
     * @return 是否支持
     */
    public boolean isDeviceTypeSupported(int deviceType) {
        return deviceType == TYPE_MODEM || 
               deviceType == TYPE_AMPLIFIER || 
               deviceType == TYPE_PDU;
    }
} 