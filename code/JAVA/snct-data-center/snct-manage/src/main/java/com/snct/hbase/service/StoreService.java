package com.snct.hbase.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.snct.common.core.redis.RedisCache;
import com.snct.hbase.domain.hbase.*;
import com.snct.hbase.utils.DateUtils;
import com.snct.hbase.utils.HBaseDaoUtil;
import com.snct.kafka.KafkaMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * HBase 存储服务类
 * <AUTHOR>
 */
@Component
public class StoreService {
    /**
     * 日志记录器
     */
    public final static Logger logger = LoggerFactory.getLogger(StoreService.class);

    /**
     * HBase 命名空间
     */
    private static final String NAMESPACE = "snct";

    /**
     * Redis缓存工具类
     */
    @Autowired
    private RedisCache redisCache;

    /**
     * HBase 数据访问工具类
     */
    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;

    /**
     * Kafka消息类型
     */
    private static final int TYPE_MODEM = 1;         // 猫数据
    private static final int TYPE_AMPLIFIER = 2;     // 功放数据
    private static final int TYPE_PDU = 3;           // PDU数据

    /**
     * 数据抽稀存储的时间间隔集合（分钟）
     * 注意：目前按时间间隔存储功能已被注释，暂时不使用
     */
    /* 
    private static final Integer[] INTERVALS = {0, 1, 5, 15, 100};
    */

    /**
     * 将 Kafka 消息保存到 HBase 中
     *
     * @param message Kafka 消息对象
     */
    public void save2Hbase(KafkaMessage message) {
        try {
            // 根据消息类型分发处理
            Integer type = message.getType();
            String code = message.getCode();
            String msgContent = message.getMsg();
            String sn = message.getSn();
            Long initialTime = message.getInitialTime() != null ? message.getInitialTime() : System.currentTimeMillis();
            
            // 设置消息的初始时间
            message.setInitialTime(initialTime);
            
            // 根据消息类型处理不同的数据
            switch (type) {
                case TYPE_MODEM:
                    saveModemData(message);
                    break;
                case TYPE_AMPLIFIER:
                    saveAmplifierData(message);
                    break;
                case TYPE_PDU:
                    savePduData(message);
                    break;
                default:
                    logger.warn("未支持的消息类型: {}, 消息代码: {}", type, code);
            }
        } catch (Exception e) {
            logger.error("保存数据到HBase时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 保存Modem数据到HBase
     *
     * @param message Kafka消息
     */
    private void saveModemData(KafkaMessage message) {
        try {
            JSONObject jsonObject = JSON.parseObject(message.getMsg());
            ModemHbaseVo modemVo = new ModemHbaseVo();
            
            // 设置基本字段
            Long deviceId = jsonObject.getLong("deviceId");
            Long deptId = jsonObject.getLong("deptId");
            Long shipId = jsonObject.getLong("shipId");
            String sn = message.getSn();
            
            modemVo.setDeptId(deptId);
            modemVo.setShipId(shipId);
            modemVo.setDeviceId(deviceId);
            modemVo.setSignal(jsonObject.getDouble("signal"));
            modemVo.setSpeed(jsonObject.getDouble("speed"));
            modemVo.setSendPower(jsonObject.getDouble("sendPower"));
            modemVo.setIsFlag(jsonObject.getLong("isFlag"));
            modemVo.setStatus(0L); // 默认状态
            
            // 保存数据
            saveVo(modemVo, message, "modem", deptId, sn, deviceId);
        } catch (Exception e) {
            logger.error("保存猫数据到HBase时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 保存功放数据到HBase
     *
     * @param message Kafka消息
     */
    private void saveAmplifierData(KafkaMessage message) {
        try {
            JSONObject jsonObject = JSON.parseObject(message.getMsg());
            AmplifierHbaseVo amplifierVo = new AmplifierHbaseVo();
            
            // 设置基本字段
            Long deviceId = jsonObject.getLong("deviceId");
            Long deptId = jsonObject.getLong("deptId");
            Long shipId = jsonObject.getLong("shipId");
            String sn = message.getSn();
            
            amplifierVo.setDeptId(deptId);
            amplifierVo.setShipId(shipId);
            amplifierVo.setDeviceId(deviceId);
            amplifierVo.setDecay(jsonObject.getDouble("decay"));
            amplifierVo.setTemp(jsonObject.getDouble("temp"));
            amplifierVo.setOutPower(jsonObject.getDouble("outPower"));
            amplifierVo.setBucStatus(jsonObject.getLong("bucStatus"));
            amplifierVo.setStatus(0L); // 默认状态
            
            // 保存数据
            saveVo(amplifierVo, message, "amplifier", deptId, sn, deviceId);
            
            logger.info("成功保存功放数据到HBase, DeviceId: {}, SN: {}", deviceId, sn);
        } catch (Exception e) {
            logger.error("保存功放数据到HBase时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 保存PDU数据到HBase（包含PDU主设备数据和通道数据）
     *
     * @param message Kafka消息
     */
    private void savePduData(KafkaMessage message) {
        try {
            JSONObject jsonObject = JSON.parseObject(message.getMsg());
            
            // 提取基本信息
            Long deptId = jsonObject.getLong("deptId");
            String batchCode = jsonObject.getString("batchCode");
            Long deviceId = jsonObject.getLong("deviceId");
            Long shipId = jsonObject.getLong("shipId");
            Long timestamp = jsonObject.getLong("timestamp");
            String sn = message.getSn();
            
            if (deviceId == null || StringUtils.isEmpty(batchCode)) {
                logger.error("PDU数据缺少必要字段: deviceId={}, batchCode={}", deviceId, batchCode);
                return;
            }
            
            // 创建PDU对象
            PduHbaseVo pduVo = new PduHbaseVo();
            pduVo.setDeptId(deptId);
            pduVo.setBatchCode(batchCode);
            pduVo.setDeviceId(deviceId);
            pduVo.setStatus(0L); // 默认状态
            
            // 从PDU主设备数据中提取信息
            JSONObject pduDataJson = jsonObject.getJSONObject("pduData");
            if (pduDataJson != null) {
                //pduVo.setDeptId(pduDataJson.getLong("deptId"));
                pduVo.setShipId(shipId);
                pduVo.setElectric(pduDataJson.getDouble("electric"));
                pduVo.setManage(pduDataJson.getDouble("manage"));
                pduVo.setVoltage(pduDataJson.getDouble("voltage"));
                pduVo.setYesPwoer(pduDataJson.getDouble("yesPwoer"));
                pduVo.setNoPwoer(pduDataJson.getDouble("noPwoer"));
                pduVo.setSeePwoer(pduDataJson.getDouble("seePwoer"));
                pduVo.setPowerParam(pduDataJson.getLong("powerParam"));
            } else {
                // 如果没有PDU主设备数据，尝试从顶层获取部门ID和船舶ID
                pduVo.setDeptId(jsonObject.getLong("deptId"));
                pduVo.setShipId(jsonObject.getLong("shipId"));
            }
            
            // 处理通道数据
            JSONArray channelDataArray = jsonObject.getJSONArray("channelData");
            if (channelDataArray != null && !channelDataArray.isEmpty()) {
                for (int i = 0; i < channelDataArray.size(); i++) {
                    JSONObject channelJson = channelDataArray.getJSONObject(i);
                    if (channelJson != null) {
                        Long outIndex = channelJson.getLong("outIndex");
                        Double electric = channelJson.getDouble("electric");
                        Double power = channelJson.getDouble("power");
                        Long outStatus = channelJson.getLong("outStatus");
                        
                        // 添加通道数据
                        pduVo.addChannelData(outIndex, electric, power, outStatus);
                    }
                }
            }
            
            // 保存到HBase
            savePduVo(pduVo, message, sn);
            
            logger.info("<<<<<<<<<<<<<<<<<<<<保存PDU数据到HBase成功, 设备ID: {}, 批次号: {}, SN: {}, 通道数: {}",
                deviceId, batchCode, sn, pduVo.hasChannelData() ? pduVo.getChannelDataMap().size() : 0);
        } catch (Exception e) {
            logger.error("保存PDU数据到HBase时发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 保存PDU对象到HBase
     * 
     * @param pduVo PDU对象
     * @param message Kafka消息
     * @param sn 船舶SN
     */
    private void savePduVo(PduHbaseVo pduVo, KafkaMessage message, String sn) {
        try {
            Long initialTime = message.getInitialTime();
            Long deviceId = pduVo.getDeviceId();
            Long deptId = pduVo.getDeptId();
            
            // 生成行键
            String rowKey = generateRowKey(deptId, sn, deviceId, initialTime);
            
            // 构造Redis key确认是否已处理
            String redisKey = deviceId + "-pdu-" + rowKey;
            
            // 检查Redis中是否存在该key，避免重复处理
            if (redisCache.hasKey(redisKey)) {
                logger.debug("PDU数据已处理，跳过存储。Key: {}", redisKey);
                return;
            }
            
            // 设置行键和时间
            pduVo.setId(rowKey);
            pduVo.setInitialBjTime(DateUtils.getDateToString(initialTime));
            
            // 构建表名
            String tableName = NAMESPACE + ":pdu";
            
            // 准备列簇集合
            Set<String> familyColumns = new HashSet<>();
            familyColumns.add("i"); // 基本信息列簇
            
            // 如果有通道数据，添加通道列簇
            if (pduVo.hasChannelData()) {
                for (Long outIndex : pduVo.getChannelDataMap().keySet()) {
                    familyColumns.add(pduVo.getOutColumnFamily(outIndex));
                }
            }
            
            // 检查表是否存在，不存在则创建
            if (!hBaseDaoUtil.tableExists(tableName)) {
                hBaseDaoUtil.createTable(tableName, familyColumns);
                logger.info("创建HBase表: {}", tableName);
            } else {
                // 表已存在，检查是否需要添加新列簇
                for (String family : familyColumns) {
                    if (!hBaseDaoUtil.columnFamilyExists(tableName, family)) {
                        hBaseDaoUtil.addColumnFamily(tableName, family);
                        logger.info("向表 {} 添加列簇: {}", tableName, family);
                    }
                }
            }
            
            // 手动构建列数据映射 - 全部使用字符串类型避免二进制存储，但保持与API兼容
            Map<String, Map<String, Object>> dataMap = new HashMap<>();
            
            // 基本信息列簇
            Map<String, Object> infoMap = new HashMap<>();
            infoMap.put("i_b_t", pduVo.getInitialBjTime());
            infoMap.put("d_p", pduVo.getDeptId() != null ? pduVo.getDeptId().toString() : "0");
            infoMap.put("s_i", pduVo.getShipId() != null ? pduVo.getShipId().toString() : "0");
            infoMap.put("d_i", pduVo.getDeviceId() != null ? pduVo.getDeviceId().toString() : "0");
            infoMap.put("b_c", pduVo.getBatchCode());
            infoMap.put("s_t", pduVo.getStatus() != null ? pduVo.getStatus().toString() : "0");
            
            // 添加PDU主设备数据
            if (pduVo.getManage() != null) infoMap.put("m_g", pduVo.getManage().toString());
            if (pduVo.getElectric() != null) infoMap.put("e_t", pduVo.getElectric().toString());
            if (pduVo.getVoltage() != null) infoMap.put("v_t", pduVo.getVoltage().toString());
            if (pduVo.getYesPwoer() != null) infoMap.put("y_p", pduVo.getYesPwoer().toString());
            if (pduVo.getNoPwoer() != null) infoMap.put("n_p", pduVo.getNoPwoer().toString());
            if (pduVo.getSeePwoer() != null) infoMap.put("s_p", pduVo.getSeePwoer().toString());
            if (pduVo.getPowerParam() != null) infoMap.put("p_p", pduVo.getPowerParam().toString());
            
            dataMap.put("i", infoMap);
            
            // 添加通道数据列簇
            if (pduVo.hasChannelData()) {
                for (Map.Entry<Long, PduHbaseVo.ChannelData> entry : pduVo.getChannelDataMap().entrySet()) {
                    Long outIndex = entry.getKey();
                    PduHbaseVo.ChannelData channelData = entry.getValue();
                    
                    String family = pduVo.getOutColumnFamily(outIndex);
                    Map<String, Object> channelMap = new HashMap<>();
                    
                    channelMap.put("o_i", channelData.getOutIndex() != null ? channelData.getOutIndex().toString() : "0");
                    channelMap.put("e_t", channelData.getElectric() != null ? channelData.getElectric().toString() : "0.0");
                    channelMap.put("p_w", channelData.getPower() != null ? channelData.getPower().toString() : "0.0");
                    channelMap.put("o_s", channelData.getOutStatus() != null ? channelData.getOutStatus().toString() : "0");
                    
                    dataMap.put(family, channelMap);
                }
            }
            
            // 保存数据到HBase
            hBaseDaoUtil.saveMappedData(tableName, rowKey, dataMap);
            
            // 设置Redis缓存标记，避免重复处理
            redisCache.setCacheObject(redisKey, initialTime, 1, TimeUnit.DAYS);

        } catch (Exception e) {
            logger.error("保存PDU数据到HBase时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 保存VO对象到HBase
     * 注意：原来的按时间间隔存储功能已被简化，只保存原始数据
     *
     * @param vo HBase VO对象
     * @param message Kafka消息
     * @param typeStr 类型字符串（用于表名）
     * @param deptId 部门ID
     * @param sn 船舶SN
     * @param deviceId 设备ID
     */
    private void saveVo(Object vo, KafkaMessage message, String typeStr, 
                        Long deptId, String sn, Long deviceId) {
        try {
            Long initialTime = message.getInitialTime();
            
            String rowKey = generateRowKey(deptId, sn, deviceId, initialTime);
            
            // 构造Redis key, 格式: 设备编码-数据类型-RowKey
            String redisKey = deviceId + "-" + typeStr + "-" + rowKey;
            
            // 检查Redis中是否存在该key，如存在则表示已处理
            if (redisCache.hasKey(redisKey)) {
                logger.debug("数据已处理，跳过存储。Key: {}", redisKey);
                return;
            }
            
            // 设置HBase VO对象的通用字段
            setValue2Vo(vo, vo.getClass(), "id", rowKey);
            setValue2Vo(vo, vo.getClass(), "initialBjTime", DateUtils.getDateToString(initialTime));
            
            // 构建表名：命名空间:设备类型
            String tableName = NAMESPACE + ":" + typeStr;

            Set<String> familyColumns = new HashSet<>();
            familyColumns.add("i");

            // 检查表是否存在
            if (!hBaseDaoUtil.tableExists(tableName)) {
                hBaseDaoUtil.createTable(tableName, familyColumns);
            }
            
            // 保存数据到HBase
            hBaseDaoUtil.save(tableName, vo);
            logger.info("<<<<<<<<<<<<<<<<<<<<保存Modem数据到HBase成功. Table: {}, RowKey: {}", tableName, rowKey);
            
            // 在Redis中设置处理标记，过期时间为1天
            redisCache.setCacheObject(redisKey, initialTime, 1, TimeUnit.DAYS);
            
        } catch (Exception e) {
            logger.error("保存数据到HBase时发生异常: {}", e.getMessage(), e);
        }
    }


    /**
     * 生成HBase RowKey
     * 新格式：部门ID_船舶SN_设备ID_日期_时间
     * 格式规范：%05d_%s_%05d_%s_%s
     *
     * @param deptId 部门ID
     * @param sn 船舶SN
     * @param deviceId 设备ID
     * @param timestamp 时间戳
     * @return 格式化的RowKey
     */
    private String generateRowKey(Long deptId, String sn, Long deviceId, Long timestamp) {
        // 检查部门ID，如果为null则使用默认值
        if (deptId == null) {
            deptId = 0L;
            logger.warn("生成RowKey时部门ID为null，使用默认值0替代");
        }
        
        // 部门ID格式化为固定长度5位，不足前面补0
        String deptIdStr = String.format("%05d", deptId);
        
        // 检查船舶SN，如果为空则使用默认值
        if (sn == null || sn.isEmpty()) {
            sn = "UNKNOWN";
            logger.warn("生成RowKey时船舶SN为null或空，使用默认值UNKNOWN替代");
        }
        
        // 检查设备ID，如果为null则使用默认值
        if (deviceId == null) {
            deviceId = 0L;
            logger.warn("生成RowKey时设备ID为null，使用默认值0替代");
        }
        
        // 设备ID格式化为固定长度5位，不足前面补0
        String deviceIdStr = String.format("%05d", deviceId);
        
        // 时间戳转换为日期和时间字符串
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat timeFormat = new SimpleDateFormat("HHmmss");
        Date date = new Date(timestamp);
        String dateStr = dateFormat.format(date);
        String timeStr = timeFormat.format(date);
        
        // 组合RowKey: 部门ID_船舶SN_设备ID_日期_时间
        return deptIdStr + "_" + sn + "_" + deviceIdStr + "_" + dateStr + "_" + timeStr;
    }

    /**
     * 使用反射获取对象的Long类型字段值
     *
     * @param obj 目标对象
     * @param fieldName 字段名称
     * @return 字段值
     */
    private Long getLongFieldValue(Object obj, String fieldName) {
        try {
            Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            Object value = field.get(obj);
            if (value instanceof Long) {
                return (Long) value;
            }
        } catch (Exception e) {
            logger.error("获取对象字段值失败, 字段: {}", fieldName, e);
        }
        return 0L;
    }

    /**
     * 使用反射给 HBase VO 对象的指定字段赋值。
     *
     * @param vo 目标 HBase VO 对象
     * @param clazz 目标对象的 Class 类型
     * @param fieldName 要赋值的字段名
     * @param value 要赋的值 (字符串类型)
     */
    private void setValue2Vo(Object vo, Class<?> clazz, String fieldName, String value) {
        try {
            // 获取字段
            Field field = clazz.getDeclaredField(fieldName);
            // 设置可访问（即使是 private）
            field.setAccessible(true);
            // 设置字段值
            field.set(vo, value);
        } catch (NoSuchFieldException e) {
            logger.error("通过反射设置字段值时出错：类 {} 中找不到字段 '{}'", clazz.getName(), fieldName, e);
        } catch (IllegalAccessException e) {
            logger.error("通过反射设置字段值时出错：无法访问类 {} 中的字段 '{}'", clazz.getName(), fieldName, e);
        } catch (Exception e) {
            logger.error("通过反射设置字段 '{}' 的值为 '{}' 时发生未知错误，类 {}。", fieldName, value, clazz.getName(), e);
        }
    }
    
    /**
     * 初始化HBase表
     * 此方法可在应用启动时调用，创建所有需要的表
     */
    public void initHBaseTables() {
        try {
            // 基本信息列簇
            Set<String> basicFamilies = new HashSet<>();
            basicFamilies.add("i"); // 信息列族
            
            // 创建基本表
            String[] basicTableTypes = {"modem", "amplifier"};
            
            for (String tableType : basicTableTypes) {
                // 创建表
                String tableName = NAMESPACE + ":" + tableType;
                if (!hBaseDaoUtil.tableExists(tableName)) {
                    logger.info("创建HBase基本表: {}", tableName);
                    hBaseDaoUtil.createTable(tableName, basicFamilies);
                }
            }
            
            // 创建PDU表（包含通道列簇）
            String pduTableName = NAMESPACE + ":pdu";
            
            if (!hBaseDaoUtil.tableExists(pduTableName)) {
                // 定义12个通道的列簇
                Set<String> pduFamilies = new HashSet<>(basicFamilies);
                for (int i = 1; i <= 12; i++) {
                    pduFamilies.add("o" + i); // 添加通道列簇：o1, o2, ..., o12
                }
                
                logger.info("创建HBase PDU表: {}", pduTableName);
                hBaseDaoUtil.createTable(pduTableName, pduFamilies);
            }
            
            // 保留旧表以便向下兼容（在旧数据迁移完成前保留）
            String[] oldTableTypes = {"pdu_out"};
            
            for (String tableType : oldTableTypes) {
                // 创建表
                String tableName = NAMESPACE + ":" + tableType;
                if (!hBaseDaoUtil.tableExists(tableName)) {
                    logger.info("创建HBase旧表(向下兼容): {}", tableName);
                    hBaseDaoUtil.createTable(tableName, basicFamilies);
                }
            }
            
            logger.info("HBase表初始化完成");
        } catch (Exception e) {
            logger.error("初始化HBase表失败: {}", e.getMessage(), e);
        }
    }
}
